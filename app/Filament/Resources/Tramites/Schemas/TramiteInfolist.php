<?php

namespace App\Filament\Resources\Tramites\Schemas;

use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Storage;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Joaopaulolndev\FilamentPdfViewer\Infolists\Components\PdfViewerEntry;

class TramiteInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('number'),
                TextEntry::make('subject'),
                TextEntry::make('origen'),
                IconEntry::make('representation')
                    ->boolean(),
                TextEntry::make('full_name'),
                TextEntry::make('first_name'),
                TextEntry::make('last_name'),
                TextEntry::make('dni'),
                TextEntry::make('phone'),
                TextEntry::make('email')
                    ->label('Email address'),
                TextEntry::make('address'),
                TextEntry::make('ruc'),
                TextEntry::make('empresa'),
                TextEntry::make('document_type_id')
                    ->numeric(),
                TextEntry::make('area_oreigen_id')
                    ->numeric(),
                TextEntry::make('user_id')
                    ->numeric(),
                TextEntry::make('folio'),
                TextEntry::make('reception_date')
                    ->date(),
                PdfViewerEntry::make('file_path')
                    ->fileUrl(Storage::url($record->file_path)),
                TextEntry::make('condition'),
                TextEntry::make('status'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
            ]);
    }
}
