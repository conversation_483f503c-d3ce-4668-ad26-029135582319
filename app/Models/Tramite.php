<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tramite extends Model
{
    /** @use HasFactory<\Database\Factories\TramiteFactory> */
    use HasFactory;
    protected $fillable = [
        'representation',
        'full_name',
        'first_name',
        'last_name',
        'dni',
        'ruc',
        'empresa',
        'phone',
        'email',
        'address',
        // datos del Documento
        'number',
        'subject',
        'origen',
        'document_type_id',
        'area_oreigen_id',
        'gestion_id',
        'user_id',
        'folio',
        'reception_date',
        'file_path',
        'condition',
        'status',
    ];
    protected $casts = [
        'representation' => 'boolean',
        'reception_date' => 'date',
        'status' => 'string',
    ];
    public function documentTypes()
    {
        return $this->belongsTo(
            TypeDocument::class,
            'document_type_id',
        );
    }
    public function areaOrigen()
    {
        return $this->belongsTo(
            Area::class,
            'area_oreigen_id',
        );
    }
    public function user()
    {
        return $this->belongsTo(
            User::class,
            'user_id',
        );
    }
}
