<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Area extends Model
{
    /** @use HasFactory<\Database\Factories\AreaFactory> */
    use HasFactory;
    protected $fillable = [
        "name",
        "code",
        "status",
    ];
    protected $casts = [
        "status" => "boolean",
    ];

    public function user()
    {
        return $this->belongsTo(
            User::class,
            'area_id'
        );
    }
}
